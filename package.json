{"name": "checkin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.4", "autoprefixer": "10.4.14", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "eslint": "8.44.0", "eslint-config-next": "^13.4.8", "framer-motion": "^10.12.18", "lucide-react": "^0.259.0", "next": "^13.4.8", "next-qrcode": "^2.5.0", "postcss": "8.4.24", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^4.10.1", "react-toastify": "^9.1.3", "tailwind-merge": "^1.13.2", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "packageManager": "yarn@3.2.3+sha512.f26f951f67de0c6a33ee381e5ff364709c87e70eb5e65c694e4facde3512f1fa80b8679e6ba31ce7d340fbb46f08dd683af9457e240f25a204be7427940d767e"}