@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --ring: 240 5% 64.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;

    --ring: 240 3.7% 15.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* Improve mobile scrolling */
  html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Better touch targets for mobile */
  button, input, select, textarea {
    touch-action: manipulation;
  }
}

/* Toggle Switch Styles */
.toggle-checkbox {
  right: 0;
  z-index: 5;
  transition: all 0.3s;
}

.toggle-checkbox:checked {
  right: 6px;
}

.toggle-label {
  transition: background-color 0.3s;
}

/* Mobile-friendly scrollbars */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Improve mobile touch feedback */
  .touch-feedback {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
  
  /* Better mobile text selection */
  .select-text {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  /* Prevent text selection where not needed */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Improve button tap targets */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }

  /* Improve modal positioning on mobile */
  .modal-mobile {
    margin: 0.5rem;
    max-height: calc(100vh - 1rem);
    max-width: calc(100vw - 1rem);
  }

  /* Improve input focus on mobile */
  input[type="search"]:focus {
    transform: none;
    zoom: 1;
  }

  /* Prevent zoom on input focus for iOS */
  input[type="search"] {
    font-size: 16px;
  }

  /* Better touch targets for list items */
  .attendee-item {
    min-height: 60px;
    padding: 12px 16px;
  }
}