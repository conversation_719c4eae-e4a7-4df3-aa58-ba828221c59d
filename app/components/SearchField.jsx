import { useEffect, useRef } from 'react';

export default function SearchField({ searchTerm, handleSearchChange, className = '', autoFocus = true }) {
  const inputRef = useRef(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleClear = () => {
    const event = {
      target: {
        value: ''
      }
    };
    handleSearchChange(event);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="search"
        placeholder="Search attendees by name, email, or company..."
        value={searchTerm}
        onChange={handleSearchChange}
        autoCapitalize="none"
        autoComplete="off"
        autoCorrect="off"
        spellCheck="false"
        inputMode="search"
        className={`w-full h-12 sm:h-14 px-4 py-3 pr-12 text-base sm:text-lg text-gray-700 placeholder-gray-500 border border-gray-300 rounded-lg focus:shadow-outline focus:border-blue-500 focus:ring-1 focus:ring-blue-500 touch-feedback select-text min-h-[44px] ${className}`}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
        {searchTerm && (
          <button
            onClick={handleClear}
            className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200 touch-feedback min-w-[44px] min-h-[44px] flex items-center justify-center"
            aria-label="Clear search"
          >
            <svg
              className="w-5 h-5 sm:w-6 sm:h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
        {!searchTerm && (
          <svg
            className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        )}
      </div>
    </div>
  );
}
