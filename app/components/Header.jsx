import { Fragment } from "react";
import { EventSelector } from ".";
import Image from 'next/image'
import logo from "@/public/shs";




export default function Header({ events, selectedEvents, handleEventChange }) {
  return (
    <header className="bg-blue-900 px-4 py-2 md:py-4 text-white mb-4 md:mb-8">
      <div className="flex flex-col space-y-2 md:flex-row md:justify-between md:items-center md:space-y-0">
        <div className="flex items-center justify-center md:justify-start">
          <Image
            src='https://economics.artba.org/img/artba-logo-white.png'
            width={200}
            height={160}
            alt="ARTBA Logo"
            className="w-auto h-8 sm:h-10 md:h-16"
          />
        </div>
        <div className="w-full md:w-auto md:min-w-[300px]">
          <EventSelector
            events={events}
            selectedEvents={selectedEvents}
            handleEventChange={handleEventChange}
          />
        </div>
      </div>
    </header>
  );
}
