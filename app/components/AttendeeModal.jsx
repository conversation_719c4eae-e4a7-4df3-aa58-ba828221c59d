import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { AgendaModal } from "./AgendaModal";

export default function AttendeeModal({ modalOpen, handleCloseModal, selectedAttendee, handleCheckIn, selectedEvent }) {
  const [isLoading, setIsLoading] = useState(false);
  const [isAgendaModalOpen, setAgendaModalOpen] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleCheckInClick = () => {
    setShowConfirmation(true);
  };

  const handleConfirmCheckIn = async () => {
    setIsLoading(true);
    setShowConfirmation(false);
    await handleCheckIn();
    setIsLoading(false);
    // Close the modal after successful check-in instead of showing agenda
    handleCloseModal();
  };

  const handleCancelCheckIn = () => {
    setShowConfirmation(false);
  };

  const handleCloseAgendaModal = () => {
    setAgendaModalOpen(false);
  };

  return (
    <div>
      <Transition appear show={modalOpen} as={Fragment}>
        <Dialog
          as="div"
          className="fixed inset-0 z-10 overflow-y-auto"
          onClose={handleCloseModal}
        >
          <div className="min-h-screen px-2 sm:px-4 text-center">
            <Dialog.Overlay className="fixed inset-0 bg-black opacity-30" />

            <span className="inline-block h-screen align-middle" aria-hidden="true">
              &#8203;
            </span>

            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <div className="inline-block w-full max-w-md p-4 sm:p-6 my-4 sm:my-8 mx-2 sm:mx-0 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl modal-mobile">
                {!showConfirmation ? (
                  <>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      Attendee Details
                    </Dialog.Title>
                    <div className="mt-4 space-y-3">
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <p className="text-sm font-medium text-gray-700">Name:</p>
                        <p className="text-base text-gray-900">{selectedAttendee?.FirstName} {selectedAttendee?.LastName}</p>
                      </div>
                      {selectedAttendee?.Email && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-gray-700">Email:</p>
                          <p className="text-base text-gray-900">{selectedAttendee.Email}</p>
                        </div>
                      )}
                      {selectedAttendee?.CompanyName && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-gray-700">Company:</p>
                          <p className="text-base text-gray-900">{selectedAttendee.CompanyName}</p>
                        </div>
                      )}
                      {selectedAttendee?.TicketName && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm font-medium text-gray-700">Ticket:</p>
                          <p className="text-base text-gray-900">{selectedAttendee.TicketName}</p>
                        </div>
                      )}
                      {selectedAttendee?.Attended && (
                        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                          <p className="text-sm font-medium text-green-700">✓ Already Checked In</p>
                        </div>
                      )}
                    </div>

                    <div className="mt-6 space-y-3">
                      {!selectedAttendee?.Attended && (
                        <button
                          type="button"
                          className="inline-flex justify-center w-full px-4 py-3 text-base font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] touch-feedback"
                          onClick={handleCheckInClick}
                          disabled={isLoading}
                        >
                          {isLoading ? "Checking In..." : "Check In"}
                        </button>
                      )}
                      <button
                        type="button"
                        className="inline-flex justify-center w-full px-4 py-3 text-base font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-gray-500 min-h-[44px] touch-feedback"
                        onClick={handleCloseModal}
                      >
                        Close
                      </button>
                    </div>
                  </>
                ) : (
                  <>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      Confirm Check-In
                    </Dialog.Title>
                    <div className="mt-4">
                      <p className="text-sm text-gray-600">
                        Are you sure you want to check in <strong>{selectedAttendee?.FirstName} {selectedAttendee?.LastName}</strong>?
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        This action cannot be undone.
                      </p>
                    </div>

                    <div className="mt-6 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                      <button
                        type="button"
                        className="flex-1 inline-flex justify-center px-4 py-3 text-base font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-green-500 min-h-[44px] touch-feedback"
                        onClick={handleConfirmCheckIn}
                        disabled={isLoading}
                      >
                        {isLoading ? "Checking In..." : "Yes, Check In"}
                      </button>
                      <button
                        type="button"
                        className="flex-1 inline-flex justify-center px-4 py-3 text-base font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-gray-500 min-h-[44px] touch-feedback"
                        onClick={handleCancelCheckIn}
                        disabled={isLoading}
                      >
                        Cancel
                      </button>
                    </div>
                  </>
                )}
              </div>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition>
      <AgendaModal modalOpen={isAgendaModalOpen} handleCloseModal={handleCloseAgendaModal} selectedEvent={selectedEvent} />
    </div>
  );
}
