"use client";
import { useState, useEffect, useCallback, useMemo } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  AttendeeModal,
  AttendeesList,
  EventSelector,
  SearchField,
  LoadingSpinner,
  ErrorMessage,
  PullToRefresh,
} from "./components";
import Header from "./components/Header";
import { Fragment } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { fetchEventData, fetchEventAttendees, fetchMultipleEventAttendees, markAttendeeAsAttended, debounce } from "./utils/api";

export default function Page() {
  const [data, setData] = useState([]);
  const [selectedEvents, setSelectedEvents] = useState([]);
  const [attendees, setAttendees] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedAttendee, setSelectedAttendee] = useState(null);
  const [response, setResponse] = useState(null);
  const [qrlink, setQrlink] = useState(null);
  const [showCheckedIn, setShowCheckedIn] = useState(false);

  const fetchInitialData = async () => {
    try {
      setInitialLoading(true);
      setError(null);
      const data = await fetchEventData();
      setData(data);

      // Get selected events from local storage
      const storedEvents = localStorage.getItem("selectedEvents");
      if (storedEvents) {
        const parsedEvents = JSON.parse(storedEvents);

        // Find these events in the newly fetched data
        const updatedEvents = parsedEvents.map(storedEvent => 
          data.find(event => event.EventUniqueId === storedEvent.EventUniqueId)
        ).filter(Boolean);

        if (updatedEvents.length > 0) {
          setSelectedEvents(updatedEvents);
          setQrlink(updatedEvents[0].Summary);
          localStorage.setItem("selectedEvents", JSON.stringify(updatedEvents));

          const eventIds = updatedEvents.map(event => event.EventUniqueId);
          const attendeesData = await fetchMultipleEventAttendees(eventIds);
          setAttendees(attendeesData);
          localStorage.setItem("attendees", JSON.stringify(attendeesData));
        } else {
          setAttendees([]);
          setSelectedEvents([]);
          setQrlink(null);
          localStorage.removeItem("selectedEvents");
          localStorage.removeItem("attendees");
        }
      }
    } catch (error) {
      console.error("Failed to fetch initial data:", error);
      setError("Failed to load events. Please check your connection and try again.");
    } finally {
      setInitialLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, []);

  const handleEventChange = async (selected) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedEvents(selected);
      
      if (selected && selected.length > 0) {
        const eventIds = selected.map(event => event.EventUniqueId);
        const attendeesData = await fetchMultipleEventAttendees(eventIds);
        setAttendees(attendeesData);
        setQrlink(selected[0].Summary);

        localStorage.setItem("selectedEvents", JSON.stringify(selected));
        localStorage.setItem("attendees", JSON.stringify(attendeesData));
      } else {
        setAttendees([]);
        setQrlink(null);
        localStorage.removeItem("selectedEvents");
        localStorage.removeItem("attendees");
      }
    } catch (error) {
      console.error("Failed to load attendees:", error);
      setError("Failed to load attendees. Please try again.");
      toast.error("Failed to load attendees");
    } finally {
      setLoading(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term) => {
      setSearchTerm(term);
    }, 100), // Reduced to 100 milliseconds
    []
  );

  const handleSearchChange = (e) => {
    // Allow natural typing with spaces - only trim leading/trailing spaces
    const term = e.target.value.trimStart();
    debouncedSearch(term);
  };

  const handleAttendeeClick = (attendee) => {
    console.log("handleAttendeeClick triggered", attendee);
    setSelectedAttendee(attendee);
    setModalOpen(true);
  };
  const handleCheckIn = async () => {
    console.log("Checking in attendee:", selectedAttendee);
    if (!selectedAttendee) return;
    
    try {
      const result = await markAttendeeAsAttended(
        selectedAttendee.AttendeeUniqueID
      );
      if (result.success) {
        const eventIds = selectedEvents.map(event => event.EventUniqueId);
        const attendeesData = await fetchMultipleEventAttendees(eventIds);
        setAttendees(attendeesData);

        toast.success("Attendee checked in successfully");
        setSearchTerm("");
        handleCloseModal();
      }
    } catch (error) {
      console.error("Failed to check in the attendee", error);
      toast.error("Failed to check in the attendee. Please try again.");
    }
  };

  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (selectedEvents && selectedEvents.length > 0) {
        const eventIds = selectedEvents.map(event => event.EventUniqueId);
        const attendeesData = await fetchMultipleEventAttendees(eventIds);
        setAttendees(attendeesData);
        setQrlink(selectedEvents[0].Summary);
        localStorage.setItem("attendees", JSON.stringify(attendeesData));
        toast.success("Data refreshed successfully");
      }
    } catch (error) {
      console.error("Failed to refresh data:", error);
      setError("Failed to refresh data. Please try again.");
      toast.error("Failed to refresh data");
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    if (selectedEvents && selectedEvents.length > 0) {
      handleRefresh();
    } else {
      fetchInitialData();
    }
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    // Clear the response state variable
    setResponse(null);
  };
  // Use useMemo to optimize filtering and sorting of attendees
  const filteredAndSortedAttendees = useMemo(() => {
    return attendees
      .filter((attendee) => {
        // Filter based on checked-in status toggle
        if (showCheckedIn) {
          if (!attendee.Attended) return false;
        } else {
          if (attendee.Attended) return false;
        }

        // Filter by search term
        const searchStr = [attendee.FirstName, attendee.LastName, attendee.Email]
          .join(" ")
          .toLowerCase();
        return searchStr.includes(searchTerm.toLowerCase());
      })
      .sort((a, b) => {
        // Sort primarily by last name for better alphabetical grouping
        const aName = `${a.LastName} ${a.FirstName}`.toLowerCase();
        const bName = `${b.LastName} ${b.FirstName}`.toLowerCase();
        const term = searchTerm.toLowerCase();

        const aIndex = aName.indexOf(term);
        const bIndex = bName.indexOf(term);

        if (aIndex === 0 && bIndex !== 0) return -1;
        if (bIndex === 0 && aIndex !== 0) return 1;

        return aName.localeCompare(bName);
      });
  }, [attendees, searchTerm, showCheckedIn]); // Only recalculate when these dependencies change

  // Stats
  const totalCount = attendees.length;
  const checkedInCount = attendees.filter((a) => a.Attended).length;

  // Get display name for selected events
  const getEventDisplayName = () => {
    if (!selectedEvents || selectedEvents.length === 0) return "...";
    if (selectedEvents.length === 1) return selectedEvents[0].Name;
    return `${selectedEvents.length} Events`;
  };

  return (
    <Fragment>
      <Header
        events={data}
        selectedEvents={selectedEvents}
        handleEventChange={handleEventChange}
      />
      <main>
        <div className="max-w-7xl mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">
          {initialLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <LoadingSpinner size="xl" className="mb-4" />
              <p className="text-gray-600">Loading events...</p>
            </div>
          ) : error && !selectedEvents.length ? (
            <ErrorMessage message={error} onRetry={handleRetry} className="mb-6" />
          ) : (
            <>
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-3">
                <h2 className="font-bold text-xl sm:text-2xl text-center sm:text-left">
                  {getEventDisplayName()} Attendees{" "}
                </h2>
                {selectedEvents && selectedEvents.length > 0 && (
                  <button
                    onClick={handleRefresh}
                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center justify-center w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Refreshing...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                        Refresh
                      </>
                    )}
                  </button>
                )}
              </div>
              
              {error && (
                <ErrorMessage message={error} onRetry={handleRetry} className="mb-4" />
              )}
              
              {/* Stats */}
              <div className="flex justify-center flex-wrap items-center gap-2 sm:gap-4 mb-4 text-sm text-center">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">Total: <strong>{totalCount}</strong></span>
                <span className="text-green-600 bg-green-100 px-2 py-1 rounded">Checked‑in: <strong>{checkedInCount}</strong></span>
                <span className="text-yellow-600 bg-yellow-100 px-2 py-1 rounded">Not checked‑in: <strong>{totalCount - checkedInCount}</strong></span>
                {searchTerm && (
                  <span className="text-blue-600 bg-blue-100 px-2 py-1 rounded">
                    Found: <strong>{filteredAndSortedAttendees.length}</strong>
                  </span>
                )}
              </div>

              {/* Toolbar */}
              <div className="flex flex-col gap-3 mb-4">
                <SearchField
                  searchTerm={searchTerm}
                  handleSearchChange={handleSearchChange}
                  className="w-full"
                />
                {selectedEvents && selectedEvents.length > 0 && (
                  <div className="flex items-center justify-center sm:justify-start">
                    <label htmlFor="checked-in-toggle" className="mr-3 text-sm font-medium text-gray-700">
                      {showCheckedIn ? "Showing Checked-in" : "Showing Not Checked-in"}
                    </label>
                    <div className="relative inline-block w-12 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        name="checked-in-toggle"
                        id="checked-in-toggle"
                        checked={showCheckedIn}
                        onChange={() => setShowCheckedIn(!showCheckedIn)}
                        className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                      />
                      <label
                        htmlFor="checked-in-toggle"
                        className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${showCheckedIn ? 'bg-green-400' : 'bg-gray-300'}`}
                      ></label>
                    </div>
                  </div>
                )}
              </div>

              {loading && !initialLoading ? (
                <>
                  <Skeleton className="w-[100%] h-[50px] rounded-full mt-2" />
                  <Skeleton className="w-[100%] h-[50px] rounded-full mt-2" />
                  <Skeleton className="w-[100%] h-[50px] rounded-full mt-2" />
                  <Skeleton className="w-[100%] h-[50px] rounded-full mt-2" />
                  <Skeleton className="w-[100%] h-[50px] rounded-full mt-2" />
                  <Skeleton className="w-[100%] h-[50px] rounded-full mt-2" />
                </>
              ) : (
                <>
                  <PullToRefresh 
                    onRefresh={handleRefresh} 
                    disabled={loading || !selectedEvents.length}
                  >
                    <AttendeesList
                      attendees={filteredAndSortedAttendees}
                      handleAttendeeClick={handleAttendeeClick}
                      searchTerm={searchTerm}
                      showCheckedIn={showCheckedIn}
                      selectedEvents={selectedEvents}
                    />
                  </PullToRefresh>
                  <AttendeeModal
                    modalOpen={modalOpen}
                    handleCloseModal={handleCloseModal}
                    selectedAttendee={selectedAttendee}
                    handleCheckIn={handleCheckIn}
                    selectedEvent={selectedEvents && selectedEvents.length > 0 ? selectedEvents[0] : null}
                  />
                </>
              )}
            </>
          )}
        </div>
      </main>
      <ToastContainer />
    </Fragment>
  );
}
